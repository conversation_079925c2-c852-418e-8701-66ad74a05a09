# fm/core/services/auto_import_manager.py

import os
import shutil
import time
from queue import Queue, Empty
from threading import Thread

from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileCreatedEvent

from fm.core.services.logger import log
from fm.modules.update_data.utils.statement_handlers._handler_registry import get_handler


class AutoImportEventHandler(FileSystemEventHandler):
    """Event handler for file system events that adds new files to a queue."""
    def __init__(self, queue):
        self.queue = queue

    def on_created(self, event):
        if isinstance(event, FileCreatedEvent):
            # A short delay to ensure the file is fully written and unlocked
            time.sleep(1)
            log.info(f"New file detected: {event.src_path}")
            self.queue.put(event.src_path)


class AutoImportManager:
    """Manages the auto-import folder monitoring service."""

    def __init__(self, config):
        """
        Initialises the AutoImportManager.

        Args:
            config: The application's configuration object.
        """
        self.config = config
        self.observer = None
        self.processing_thread = None
        self.file_queue = Queue()
        self.is_running = False

        # Extract settings from config
        self.enabled = self.config.getboolean('auto_import', 'enabled', fallback=False)
        self.import_path = self.config.get('auto_import', 'import_path', fallback=None)
        self.archive_path = self.config.get('auto_import', 'archive_path', fallback=None)
        self.failed_path = self.config.get('auto_import', 'failed_path', fallback=None)

        log.info(f"AutoImportManager initialised. Service enabled: {self.enabled}")

    def _ensure_directories_exist(self):
        """Create archive and failed import directories if they don't exist."""
        if not os.path.exists(self.archive_path):
            os.makedirs(self.archive_path)
            log.info(f"Created archive directory: {self.archive_path}")
        if not os.path.exists(self.failed_path):
            os.makedirs(self.failed_path)
            log.info(f"Created failed imports directory: {self.failed_path}")

    def _move_file(self, src_path, dest_folder):
        """Move a file to a destination folder, handling potential name clashes."""
        try:
            filename = os.path.basename(src_path)
            dest_path = os.path.join(dest_folder, filename)
            shutil.move(src_path, dest_path)
            log.info(f"Moved '{filename}' to '{dest_folder}'")
        except Exception as e:
            log.error(f"Failed to move file {src_path} to {dest_folder}: {e}")

    def _process_queue(self):
        """Worker method to process files from the queue."""
        while self.is_running:
            try:
                file_path = self.file_queue.get(timeout=1)
            except Empty:
                continue

            handler = get_handler(file_path)
            if not handler:
                log.warning(f"No suitable handler found for {os.path.basename(file_path)}. Moving to failed.")
                self._move_file(file_path, self.failed_path)
                self.file_queue.task_done()
                continue

            try:
                log.info(f"Processing '{os.path.basename(file_path)}' with {handler.__class__.__name__}")
                processed_df = handler.process_file(file_path)

                if processed_df is not None and not processed_df.empty:
                    log.info(f"Successfully processed {os.path.basename(file_path)}.")
                    # TODO: Integrate with database update if required
                    self._move_file(file_path, self.archive_path)
                else:
                    log.warning(f"Processing returned no data for {os.path.basename(file_path)}. Moving to failed.")
                    self._move_file(file_path, self.failed_path)

            except Exception as e:
                log.error(f"Error processing file {os.path.basename(file_path)}: {e}")
                self._move_file(file_path, self.failed_path)
            finally:
                self.file_queue.task_done()

    def start(self):
        """Starts the file system monitoring service."""
        if not self.enabled:
            log.info("Auto-import service is disabled in config. Not starting.")
            return

        if not all([self.import_path, self.archive_path, self.failed_path]):
            log.error("Auto-import paths not fully configured. Service cannot start.")
            return

        if self.is_running:
            log.warning("Auto-import service is already running.")
            return

        self._ensure_directories_exist()

        log.info(f"Starting auto-import service on path: {self.import_path}")
        self.is_running = True

        # Start file processing thread
        self.processing_thread = Thread(target=self._process_queue, daemon=True)
        self.processing_thread.start()

        # Start watchdog observer
        event_handler = AutoImportEventHandler(self.file_queue)
        self.observer = Observer()
        self.observer.schedule(event_handler, self.import_path, recursive=False)
        self.observer.start()
        log.info("Auto-import service started successfully.")

    def stop(self):
        """Stops the file system monitoring service."""
        if not self.is_running:
            return

        log.info("Stopping auto-import service...")
        self.is_running = False # Signal processing thread to stop

        if self.observer:
            self.observer.stop()
            self.observer.join() # Wait for observer to shut down

        if self.processing_thread:
            self.processing_thread.join() # Wait for processing thread to finish

        log.info("Auto-import service stopped.")

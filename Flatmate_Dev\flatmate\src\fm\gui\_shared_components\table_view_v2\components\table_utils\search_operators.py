"""
Search Operators Configuration

Centralized definition of logical operators and synonyms for search functionality.
This is the single source of truth for all search complexity detection.
"""

from typing import List, Dict, Set
from dataclasses import dataclass


@dataclass
class SearchOperatorConfig:
    """Configuration for search operators and complexity detection."""

    # Character-based operators (highest priority for detection)
    character_operators: List[str]

    # Keyword operators with synonyms
    keyword_operators: Dict[str, List[str]]

    # Operators that should use live filtering (not complex)
    live_filter_allowed: List[str]

    # Operators that should be ignored in certain contexts
    ignored_patterns: List[str]


# Default search operator configuration
DEFAULT_SEARCH_OPERATORS = SearchOperatorConfig(
    character_operators=[
        '|',    # OR operator (pipe)
        '(',    # Group start
        ')',    # Group end
        '&',    # AND operator
        '"',    # Quoted strings
        '+',    # Required terms
        '*',    # Wildcard
        '~',    # Fuzzy search
        '/',    # Alternative OR operator
    ],

    keyword_operators={
        'OR': ['OR', 'or', '|', '/'],
        'AND': ['AND', 'and', '&', '+'],
        'NOT': ['NOT', 'not', '!'],  # Removed '-' from here
    },

    live_filter_allowed=[
        '-',    # Dash for exclusion (e.g., "tea -coffee") - should use live filtering
    ],

    ignored_patterns=[
        # Patterns that look like operators but should be ignored in certain contexts
        'OR...PHEUS',  # Example from PM notes - "OR" in "ORPHEUS" should be ignored
        'AND...ERSON', # Similar pattern for "ANDERSON"
    ]
)


class SearchComplexityDetector:
    """Detects search query complexity using centralized operator definitions."""
    
    def __init__(self, config: SearchOperatorConfig = None):
        """Initialize with operator configuration."""
        self.config = config or DEFAULT_SEARCH_OPERATORS
    
    def is_simple_query(self, query: str) -> bool:
        """Determine if query is simple enough for live filtering.
        
        Args:
            query: User input query string
            
        Returns:
            True if query can be handled with simple string matching
        """
        if not query or not query.strip():
            return True
        
        # Check for character-based operators first (fastest)
        if self._has_character_operators(query):
            return False
        
        # Check for keyword operators with word boundaries
        if self._has_keyword_operators(query):
            return False

        # Check if query only contains live-filter-allowed operators
        if self._has_only_live_filter_operators(query):
            return True  # These are simple enough for live filtering
        
        return True
    
    def _has_character_operators(self, query: str) -> bool:
        """Check for character-based operators."""
        return any(op in query for op in self.config.character_operators)
    
    def _has_keyword_operators(self, query: str) -> bool:
        """Check for keyword operators with word boundary validation."""
        import re
        
        query_upper = query.upper()
        
        for _, synonyms in self.config.keyword_operators.items():
            for synonym in synonyms:
                # Skip character operators (handled separately)
                if len(synonym) == 1:
                    continue
                
                # Check if keyword appears as whole word
                if re.search(r'\b' + re.escape(synonym.upper()) + r'\b', query_upper):
                    # Check if it's in an ignored pattern
                    if not self._is_ignored_pattern(query, synonym):
                        return True
        
        return False
    
    def _has_only_live_filter_operators(self, query: str) -> bool:
        """Check if query contains only operators that are allowed in live filtering.

        For example, dash operators like 'tea -coffee' should use live filtering.
        """
        # Check if query contains any live-filter-allowed operators
        has_live_filter_ops = any(op in query for op in self.config.live_filter_allowed)

        if not has_live_filter_ops:
            return False  # No live filter operators found

        # Check if query contains any complex operators that would override live filtering
        has_complex_chars = any(op in query for op in self.config.character_operators)
        if has_complex_chars:
            return False  # Complex operators take precedence

        # Check for complex keyword operators
        if self._has_keyword_operators(query):
            return False  # Complex keywords take precedence

        return True  # Only live-filter operators found
    
    def _is_ignored_pattern(self, query: str, operator: str) -> bool:
        """Check if operator appears in an ignored pattern context."""
        # This could be expanded to handle more sophisticated pattern matching
        # For now, just check if the operator is part of a larger word
        
        # Simple heuristic: if operator is surrounded by letters, it might be part of a word
        import re
        
        # Find all occurrences of the operator
        for match in re.finditer(re.escape(operator), query, re.IGNORECASE):
            start, end = match.span()
            
            # Check if it's surrounded by word characters
            before_char = query[start-1] if start > 0 else ' '
            after_char = query[end] if end < len(query) else ' '
            
            # If both before and after are word characters, it's likely part of a word
            if before_char.isalnum() and after_char.isalnum():
                continue  # This occurrence is ignored
            else:
                return False  # Found a valid operator occurrence
        
        return True  # All occurrences were ignored


# Global instance for easy access
search_complexity_detector = SearchComplexityDetector()


def is_simple_query(query: str) -> bool:
    """Convenience function for simple query detection.
    
    Args:
        query: User input query string
        
    Returns:
        True if query can be handled with simple string matching
    """
    return search_complexity_detector.is_simple_query(query)


def get_all_operators() -> List[str]:
    """Get all operators for debugging/testing purposes."""
    config = DEFAULT_SEARCH_OPERATORS
    all_ops = config.character_operators.copy()
    
    for synonyms in config.keyword_operators.values():
        all_ops.extend(synonyms)
    
    all_ops.extend(config.live_filter_allowed)
    
    return list(set(all_ops))  # Remove duplicates


def get_operator_config() -> SearchOperatorConfig:
    """Get the current operator configuration."""
    return DEFAULT_SEARCH_OPERATORS

# Technical Design: Table Search & Filtering

## Current Architecture
- Table view implemented in Categorise UI (exact file/class to be referenced in codebase analysis)
- Search bar triggers live filtering logic (AND/exclude supported)
- Filtering and sorting handled client-side

## Required Changes
- Modify filtering logic to detect operator/operator-character and disable live filtering
- Add visual cue/button for explicit apply
- Hide internal/system columns from user view
- Optimise for performance and error handling

## Integration Points
- Table search bar input handler
- Filtering logic in table model/controller
- UI component for "Apply" button/visual cue

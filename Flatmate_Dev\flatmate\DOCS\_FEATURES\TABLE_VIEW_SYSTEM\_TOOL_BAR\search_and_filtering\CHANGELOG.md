# Table View Search and Filtering - Changelog

## [2025-07-20] - Critical Fix: All Columns Search TypeError

### 🐛 Fixed
- **CRITICAL**: Fixed TypeError when using "all columns" search functionality
  - Root cause: `EnhancedFilterProxyModel` was passing string `"all_columns"` to `model.index()` which expects integer
  - Impact: Complete failure of global search across all table views
  - Solution: Added special case handling for `"all_columns"` and `"all_visible"` strings

### ✨ Added
- **New Feature**: "All Visible Columns" search option
  - Replaces generic "All Columns" with more descriptive "All Visible Columns"
  - Maintains backward compatibility with existing `"all_columns"` usage
  - Integrates with show/hide columns functionality

- **Enhanced Column Filtering**: Smart exclusion of system columns
  - Automatically excludes: `db_uid`, `date_modified`, `date_created`, `hash`, `id`, `uid`
  - Improves search relevance by focusing on user-meaningful data
  - Configurable exclusion patterns for future extensibility

- **Complex Query Dispatcher Integration**: 
  - All columns search now properly routed through complex query dispatcher
  - Triggers apply button with 150ms debouncing to prevent UI freezes
  - Maintains live filtering for simple queries on single columns

### 🎨 Improved
- **Apply Button UX**: Updated icon from check mark to keyboard return
  - More intuitive indication that Enter key applies the filter
  - Updated tooltip to "Apply filter (Enter)" for clarity
  - Fallback handling if keyboard return icon fails to load

- **Performance Optimization**: 
  - Debounced complex query detection prevents UI blocking during typing
  - Efficient column exclusion using header-based pattern matching
  - No performance regression for existing single-column filtering

### 🔧 Technical Changes
- **Enhanced Error Handling**: Added try/catch blocks with graceful fallbacks
- **Improved Code Documentation**: Clear method descriptions and parameter documentation
- **Test Coverage**: Comprehensive automated test suite for regression prevention

### 📁 Files Modified
- `enhanced_filter_proxy_model.py` - Core filtering logic fix
- `table_view_toolbar_v2.py` - Complex query detection and routing  
- `table_view_core.py` - Column handling for all_columns/all_visible
- `integrated_search_field.py` - Apply button icon update
- `filter_group.py` - ApplyButton class icon update
- `base_toolbar_button.py` - Factory function icon update

### 🧪 Testing
- **Automated Test Suite**: Created comprehensive test scripts
  - `test_table_filtering_fix.py` - Core functionality verification
  - `test_apply_button_logic.py` - Complex query detection verification
- **All Tests Passing**: ✅ 100% success rate on automated tests
- **Manual Verification**: Confirmed fix resolves original TypeError

### 🔄 Migration Notes
- **Backward Compatible**: Existing `"all_columns"` usage continues to work
- **No Breaking Changes**: All existing APIs maintained
- **Automatic Upgrade**: New functionality available immediately

### 🎯 Impact
- **User Experience**: Global search functionality restored and enhanced
- **Performance**: No degradation, improved responsiveness for complex queries  
- **Reliability**: Comprehensive error handling prevents future similar issues
- **Maintainability**: Clear code structure and documentation for future development

---

## Previous Entries

### [2025-07-15] - Search Query Parser Enhancement
- Added luqum package integration for advanced boolean search
- Implemented hybrid query routing for performance optimization
- Added support for OR, AND, NOT operators with parentheses

### [2025-07-10] - Toolbar Layout Refinements  
- Modernized toolbar layout with proper component spacing
- Added dynamic apply button for complex queries
- Improved visual feedback with green border for advanced mode

### [2025-07-05] - Initial Implementation
- Basic search functionality with column selection
- Live filtering for simple text queries
- Column visibility controls integration

# Implementation Tasks: Table Search & Filtering

## Task 1: Detect Operator Characters in Input
**File**: [to be determined]
**Method**: [to be determined]
**Time**: 20 minutes
**Dependencies**: None

**Current Code**: (to be retrieved)

**New Code**: (to be implemented)

**Testing**:
- Enter an operator character (`O`, `R`, `/`, `(`, `)`) and verify live filtering stops

---

## Task 2: Show Visual Cue/Button When Live Filtering Disabled
**File**: [to be determined]
**Method**: [to be determined]
**Time**: 20 minutes
**Dependencies**: Task 1

**Current Code**: (to be retrieved)

**New Code**: (to be implemented)

**Testing**:
- Visual cue/button appears when advanced logic entered

---

## Task 3: Hide Internal/System Columns
**File**: [to be determined]
**Method**: [to be determined]
**Time**: 20 minutes
**Dependencies**: None

**Current Code**: (to be retrieved)

**New Code**: (to be implemented)

**Testing**:
- Internal columns are not visible in table

---

## Task 4: Optimise Filtering Logic for Performance
**File**: [to be determined]
**Method**: [to be determined]
**Time**: 20 minutes
**Dependencies**: Task 1, Task 2

**Current Code**: (to be retrieved)

**New Code**: (to be implemented)

**Testing**:
- No UI lag or freeze with any filter
